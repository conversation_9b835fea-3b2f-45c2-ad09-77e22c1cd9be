import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AIAgent from './AIAgent';
import QuestionnaireLoader from './QuestionnaireLoader';
import { saveQuestionnaireResponses, getAllCompletedQuestionnaires, getQuestionnaireResponses } from '../supabase/client';
import { useAuth } from '../context/AuthContext';

export default function ClientAcquisition() {
  const [activeQuestionnaire, setActiveQuestionnaire] = useState(null);
  const [submissionResult, setSubmissionResult] = useState(null);
  const [generatingStrategy, setGeneratingStrategy] = useState(false);
  const navigate = useNavigate();
  const { user } = useAuth();

  // Track which questionnaires have been completed
  const [completedQuestionnaires, setCompletedQuestionnaires] = useState({
    leadGeneration: false,
    salesFunnel: false,
    marketingCampaigns: false,
    customerOnboarding: false
  });

  // Track which questionnaires have actual responses saved
  const [actualCompletedQuestionnaires, setActualCompletedQuestionnaires] = useState({
    leadGeneration: false,
    salesFunnel: false,
    marketingCampaigns: false,
    customerOnboarding: false
  });

  // Loading state for initial data fetch
  const [loading, setLoading] = useState(true);

  // Load completed questionnaires from Supabase on component mount
  useEffect(() => {
    const loadCompletedQuestionnaires = async () => {
      try {
        setLoading(true);
        if (user) {
          const { completionStatus, error } = await getAllCompletedQuestionnaires(true);

          if (error) {
            console.error('Error loading completed questionnaires:', error);
            fallbackToLocalStorage();
          } else if (completionStatus) {
            const acquisitionStatus = {
              leadGeneration: completionStatus.leadGeneration || false,
              salesFunnel: completionStatus.salesFunnel || false,
              marketingCampaigns: completionStatus.marketingCampaigns || false,
              customerOnboarding: completionStatus.customerOnboarding || false
            };
            setCompletedQuestionnaires(acquisitionStatus);
            setActualCompletedQuestionnaires(acquisitionStatus);
          }
        } else {
          fallbackToLocalStorage();
        }
      } catch (err) {
        console.error('Error in loadCompletedQuestionnaires:', err);
        fallbackToLocalStorage();
      } finally {
        setLoading(false);
      }
    };

    const fallbackToLocalStorage = () => {
      const saved = localStorage.getItem('completedQuestionnaires_acquisition');
      if (saved) {
        const localData = JSON.parse(saved);
        setCompletedQuestionnaires(localData);

        setActualCompletedQuestionnaires({
          leadGeneration: !!localStorage.getItem('questionnaire_responses_leadGeneration'),
          salesFunnel: !!localStorage.getItem('questionnaire_responses_salesFunnel'),
          marketingCampaigns: !!localStorage.getItem('questionnaire_responses_marketingCampaigns'),
          customerOnboarding: !!localStorage.getItem('questionnaire_responses_customerOnboarding')
        });
      }
    };

    loadCompletedQuestionnaires();
  }, [user]);

  // Helper function to get questionnaire display name
  const getQuestionnaireNameFromKey = (key) => {
    const nameMap = {
      leadGeneration: 'Lead Generation Questionnaire',
      salesFunnel: 'Sales Funnel Questionnaire',
      marketingCampaigns: 'Marketing Campaigns Questionnaire',
      customerOnboarding: 'Customer Onboarding Questionnaire'
    };
    return nameMap[key] || key;
  };

  // Questionnaire configurations
  const questionnaireConfigs = {
    leadGeneration: {
      title: "Lead Generation Questionnaire",
      description: "This questionnaire helps develop effective strategies to generate quality leads.",
      files: ['lead-generation_strategy-questionnaire-01.yaml'],
      defaultFile: 'lead-generation_strategy-questionnaire-01.yaml'
    },
    salesFunnel: {
      title: "Customer Acquisition Strategy Questionnaire",
      description: "This questionnaire aims to gather essential information about your current and desired customer acquisition strategies.",
      files: ['customer-acquisition-strategy-questionnaire.yaml'],
      defaultFile: 'customer-acquisition-strategy-questionnaire.yaml'
    },
    marketingCampaigns: {
      title: "Marketing Campaigns Design Questionnaire",
      description: "This questionnaire will guide you through designing targeted marketing campaigns to effectively attract potential clients for your spiritual fine jewelry business.",
      files: ['marketing-campaigns-design-questionnaire.yaml'],
      defaultFile: 'marketing-campaigns-design-questionnaire.yaml'
    },
    customerOnboarding: {
      title: "Customer Onboarding Improvement Questionnaire",
      description: "This questionnaire will guide you through assessing and improving your customer onboarding process to increase retention and customer lifetime value.",
      files: ['customer-onboarding-improvement-questionnaire.yaml'],
      defaultFile: 'customer-onboarding-improvement-questionnaire.yaml'
    }
  };

  // Handle questionnaire submission
  const handleSubmit = async (data) => {
    console.log('Questionnaire submitted:', data);

    if (activeQuestionnaire) {
      const updatedCompletedQuestionnaires = {
        ...completedQuestionnaires,
        [activeQuestionnaire]: true
      };

      setCompletedQuestionnaires(updatedCompletedQuestionnaires);
      localStorage.setItem('completedQuestionnaires_acquisition', JSON.stringify(updatedCompletedQuestionnaires));

      const questionnaireName = getQuestionnaireNameFromKey(activeQuestionnaire);

      const responseData = {
        questionnaire: questionnaireName,
        responses: data,
        timestamp: new Date().toISOString(),
        user_id: user?.id || null
      };

      if (user) {
        try {
          const { error } = await saveQuestionnaireResponses(
            activeQuestionnaire,
            questionnaireName,
            data,
            user.id
          );

          if (error) {
            console.error('Error saving to Supabase:', error);
            localStorage.setItem(`questionnaire_responses_${activeQuestionnaire}`, JSON.stringify(responseData));
          } else {
            setActualCompletedQuestionnaires(prev => ({
              ...prev,
              [activeQuestionnaire]: true
            }));
          }

          sessionStorage.setItem('questionnaire_view_responses', JSON.stringify(responseData));
        } catch (err) {
          console.error('Error in handleSubmit:', err);
          localStorage.setItem(`questionnaire_responses_${activeQuestionnaire}`, JSON.stringify(responseData));
        }
      } else {
        localStorage.setItem(`questionnaire_responses_${activeQuestionnaire}`, JSON.stringify(responseData));

        setActualCompletedQuestionnaires(prev => ({
          ...prev,
          [activeQuestionnaire]: true
        }));

        sessionStorage.setItem('questionnaire_view_responses', JSON.stringify(responseData));
      }
    }

    setSubmissionResult({
      success: true,
      message: 'Thank you for your submission!',
      data: data
    });
    setActiveQuestionnaire(null);
  };
  
  // Handle strategy generation
  const handleGenerateStrategy = (data) => {
    console.log('Generating strategy from data:', data);
    setGeneratingStrategy(true);

    // Simulate API call to generate strategy
    setTimeout(() => {
      setSubmissionResult({
        success: true,
        message: 'Your client acquisition strategy has been generated!',
        data: data,
        isStrategy: true
      });
      setGeneratingStrategy(false);
      setActiveQuestionnaire(null);
    }, 2000);
  };

  // Toggle questionnaire visibility
  const toggleQuestionnaire = (questionnaireType) => {
    console.log('Toggling questionnaire:', questionnaireType, 'Current active:', activeQuestionnaire);
    setActiveQuestionnaire(activeQuestionnaire === questionnaireType ? null : questionnaireType);
  };
  // Context prompt specific to client acquisition
  const contextPrompt = `You are an expert in client acquisition strategies and techniques. 
  Provide detailed, actionable advice with examples when possible. 
  Focus on practical methods for identifying, attracting, and converting potential clients. 
  Include industry best practices, data-driven approaches, and innovative strategies 
  that businesses can implement to improve their client acquisition efforts.`;

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="space-y-8">
        <div className="w-full max-w-4xl p-6 bg-white rounded shadow-md text-center mx-auto">
          <h2 className="raleway-title-h2 mb-4">Client Acquisition Strategies</h2>
          <p className="body-text mb-4">
            Comprehensive tools and methodologies to attract, engage, and convert potential clients for your business.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div
              className={`p-4 border rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer ${activeQuestionnaire === 'leadGeneration' ? 'ring-2 ring-amber-400 bg-amber-100' : 'bg-amber-50 hover:bg-amber-100'}`}
              onClick={() => toggleQuestionnaire('leadGeneration')}
            >
              <h3 className="raleway-title-h3 mb-2 text-amber-800">Lead Generation</h3>
              <p className="body-text">Develop effective strategies to generate quality leads.</p>
            </div>
            <div
              className={`p-4 border rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer ${activeQuestionnaire === 'salesFunnel' ? 'ring-2 ring-amber-400 bg-amber-100' : 'bg-amber-50 hover:bg-amber-100'}`}
              onClick={() => toggleQuestionnaire('salesFunnel')}
            >
              <h3 className="raleway-title-h3 mb-2 text-amber-800">Customer Acquisition Strategy</h3>
              <p className="body-text">Gather essential information about your current and desired customer acquisition strategies.</p>
            </div>
            <div
              className={`p-4 border rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer ${activeQuestionnaire === 'marketingCampaigns' ? 'ring-2 ring-amber-400 bg-amber-100' : 'bg-amber-50 hover:bg-amber-100'}`}
              onClick={() => toggleQuestionnaire('marketingCampaigns')}
            >
              <h3 className="raleway-title-h3 mb-2 text-amber-800">Marketing Campaigns</h3>
              <p className="body-text">Design targeted marketing campaigns to effectively attract potential clients for your spiritual fine jewelry business.</p>
            </div>
            <div
              className={`p-4 border rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer ${activeQuestionnaire === 'customerOnboarding' ? 'ring-2 ring-amber-400 bg-amber-100' : 'bg-amber-50 hover:bg-amber-100'}`}
              onClick={() => toggleQuestionnaire('customerOnboarding')}
            >
              <h3 className="raleway-title-h3 mb-2 text-amber-800">Customer Onboarding</h3>
              <p className="body-text">Assess and improve your customer onboarding process to increase retention and customer lifetime value.</p>
            </div>
          </div>

          {/* Questionnaire Completion Status */}
          <div className="mt-8 border-t pt-4">
            <h4 className="raleway-title-h4 mb-3">Questionnaire Completion Status:</h4>
            <div className="flex flex-col space-y-4">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center">
                  <div className="completion-indicator">
                    {completedQuestionnaires.leadGeneration ? (
                      <div className="completion-badge bg-amber-500">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                    ) : (
                      <div className="completion-badge bg-gray-200">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </div>
                    )}
                  </div>
                  <div className="ml-3">
                    <h5 className="raleway-title-h5 text-amber-800">Lead Generation</h5>
                    <p className="text-sm text-gray-600">{completedQuestionnaires.leadGeneration ? "Completed" : "Not completed"}</p>
                  </div>
                </div>
                {actualCompletedQuestionnaires.leadGeneration && (
                  <button
                    className="px-3 py-1 bg-amber-600 text-white text-sm rounded hover:bg-amber-700 transition flex items-center"
                    onClick={async () => {
                      try {
                        const { data, error } = await getQuestionnaireResponses('leadGeneration');

                        if (error || !data) {
                          const storedData = localStorage.getItem('questionnaire_responses_leadGeneration');
                          if (storedData) {
                            sessionStorage.setItem('questionnaire_view_responses', storedData);
                            navigate('/responses');
                          }
                        } else {
                          const formattedData = {
                            questionnaire: data.questionnaire_name,
                            responses: data.responses,
                            timestamp: data.created_at
                          };
                          sessionStorage.setItem('questionnaire_view_responses', JSON.stringify(formattedData));
                          navigate('/responses');
                        }
                      } catch (err) {
                        console.error('Error viewing responses:', err);
                      }
                    }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    View Responses
                  </button>
                )}
              </div>
            </div>

            {/* Strategy Generation Section */}
            <div className="mt-8 p-6 bg-white rounded-lg border border-amber-200 shadow-md">
              <h4 className="raleway-title-h4 mb-3 text-amber-800">GENERATE CLIENT ACQUISITION STRATEGY</h4>
              <p className="body-text mb-4">
                Ready to turn your questionnaire responses into an actionable client acquisition strategy?
                Click the button below to generate a comprehensive strategy tailored to your business needs.
              </p>
              <div className="flex flex-wrap gap-4">
                <button
                  className="px-6 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition flex items-center"
                  onClick={() => {
                    const responseData = {
                      questionnaire: 'Client Acquisition Questionnaires',
                      responses: completedQuestionnaires,
                      timestamp: new Date().toISOString()
                    };
                    sessionStorage.setItem('questionnaire_responses', JSON.stringify(responseData));
                    navigate('/strategy');
                  }}
                  disabled={!Object.values(completedQuestionnaires).some(value => value)}
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                  </svg>
                  Generate Strategy
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Questionnaire Section */}
        {activeQuestionnaire && (
          <div className="mb-8 p-6 bg-white rounded-lg border border-gray-200 shadow-md">
            <div className="flex justify-end mb-4">
              <button
                onClick={() => setActiveQuestionnaire(null)}
                className="text-gray-500 hover:text-gray-700"
                aria-label="Close questionnaire"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <QuestionnaireLoader
              key={activeQuestionnaire}
              title={questionnaireConfigs[activeQuestionnaire].title}
              description={questionnaireConfigs[activeQuestionnaire].description}
              specificQuestionnaires={questionnaireConfigs[activeQuestionnaire].files}
              defaultQuestionnaire={questionnaireConfigs[activeQuestionnaire].defaultFile}
              onSubmit={handleSubmit}
              onGenerateStrategy={handleGenerateStrategy}
              showLocalSave={false}
              hideGenerateStrategyButton={true}
              hideQuestionnaireSelector={true}
            />
          </div>
        )}

        {/* Questionnaire Success Message Popup */}
        {submissionResult && (
          <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="absolute inset-0 bg-black opacity-30"></div>
            <div className="bg-white p-6 rounded-lg shadow-xl border border-green-200 z-10 max-w-md w-full mx-4">
              <div className="flex items-center justify-center mb-4 text-green-500">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold mb-2 text-center text-green-700">
                {submissionResult.message}
              </h2>
              <button
                className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition w-full"
                onClick={() => setSubmissionResult(null)}
              >
                Close
              </button>
            </div>
          </div>
        )}

        {/* Strategy Generation Loading */}
        {generatingStrategy && (
          <div className="bg-blue-50 p-6 rounded-lg shadow-md border border-blue-200 mb-8 text-center">
            <div className="animate-pulse flex flex-col items-center">
              <svg className="animate-spin h-10 w-10 text-blue-600 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <h2 className="text-xl font-semibold mb-2 text-blue-700">Generating Your Strategy</h2>
              <p className="body-text">We're analyzing your responses and creating a customized client acquisition strategy...</p>
            </div>
          </div>
        )}

        {/* Client Acquisition AI Agent */}
        <AIAgent
          title="Client Acquisition Assistant"
          description="Ask questions about client acquisition strategies, techniques, and best practices."
          contextPrompt={contextPrompt}
        />
      </div>
    </div>
  );
}
